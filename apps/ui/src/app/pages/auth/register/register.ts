import { CommonModule } from '@angular/common';
import { Component, inject } from '@angular/core';
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import {
  Auth,
  createUserWithEmailAndPassword,
  updateProfile,
} from '@angular/fire/auth';
import { RouterLink } from '@angular/router';
import { toastConfig$ } from '../../../store/signals';
import { Functions, httpsCallable } from '@angular/fire/functions';

@Component({
  selector: 'app-register',
  imports: [ReactiveFormsModule, CommonModule, RouterLink],
  templateUrl: './register.html'
})
export class Register {
  private auth: Auth = inject(Auth);
  private functions: Functions = inject(Functions);

  registerForm = new FormGroup({
    name: new FormControl('', [Validators.required]),
    email: new FormControl('', [Validators.required]),
    password: new FormControl('', [Validators.required]),
    role: new FormControl('', [Validators.required]),
  });

  async register() {
    if (this.registerForm.valid) {
      const data: any = this.registerForm.value;
      console.log('Register data:', data);
      try {
        const userCredential = await createUserWithEmailAndPassword(
          this.auth,
          data.email,
          data.password
        );
        await updateProfile(userCredential.user, { displayName: data.name });
        await this.updateUserRole(userCredential.user.uid, data.role);
        console.log('Register response:', userCredential);
      } catch (e: any) {
        console.error('Error registering:', e.code, e.message, e);
        toastConfig$.set({ status: 'error', message: e.code });
      }
    } else {
      console.error('Form is invalid');
      toastConfig$.set({ status: 'error', message: 'All fields required' });
    }
  }

  async updateUserRole(uid: string, role: string) {
    try {
      const updateUserRole$ = httpsCallable(this.functions, 'updateUserRole');
      const result = await updateUserRole$({ uid, role });
      console.log(result.data);
    } catch (error) {
      console.error(error);
    }
  }
}
