<div class="flex justify-center items-start pt-10 h-screen bg-base-300">
  <div class="card w-96 bg-base-100 shadow-xl">
    <div class="card-body">
      <h2 class="card-title justify-center">Register</h2>
      <form (ngSubmit)="register()" [formGroup]="registerForm">
        <fieldset class="form-control">
          <legend class="label">
            <span class="label-text">Name</span>
          </legend>
          <input class="input input-bordered" formControlName="name" placeholder="Firstname, Lastname" type="text" />
          @if (registerForm.get('name')?.invalid && registerForm.get('name')?.touched) {
            <div class="text-error text-sm mt-1">Name is required.</div>
          }
        </fieldset>
        <fieldset class="form-control mt-4">
          <legend class="label">
            <span class="label-text">Email</span>
          </legend>
          <input class="input input-bordered" formControlName="email" placeholder="email" type="email" />
          @if (registerForm.get('email')?.invalid && registerForm.get('email')?.touched) {
            <div class="text-error text-sm mt-1">Email is required.</div>
          }
        </fieldset>
        <fieldset class="form-control mt-4">
          <legend class="label">
            <span class="label-text">Password</span>
          </legend>
          <input class="input input-bordered" formControlName="password" placeholder="password" type="password" />
          @if (registerForm.get('password')?.invalid && registerForm.get('password')?.touched) {
            <div class="text-error text-sm mt-1">Password is required.</div>
          }
        </fieldset>
        <fieldset class="form-control mt-4">
          <legend class="label">
            <span class="label-text">Role</span>
          </legend>
          <select class="select select-bordered" formControlName="role">
            <option disabled value="">Select your role</option>
            <option>student</option>
            <option>trainer</option>
            <option>parent</option>
            <option>college-staff</option>
          </select>
          @if (registerForm.get('role')?.invalid && registerForm.get('role')?.touched) {
            <div class="text-error text-sm mt-1">Role is required.</div>
          }
        </fieldset>
        <div class="form-control mt-6">
          <button class="btn btn-primary btn-block" type="submit">Register</button>
        </div>
      </form>
      <div class="divider">Already have an account?</div>
      <a class="btn btn-outline btn-primary" routerLink="/auth/login">Login</a>
    </div>
  </div>
</div>
