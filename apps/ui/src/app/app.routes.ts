import { Route } from '@angular/router';
import { MainLayoutComponent } from './components/layouts/main-layout/main-layout.component';
import { LandingLayoutComponent } from './components/layouts/landing-layout/landing-layout.component';
import { AuthLayoutComponent } from './components/layouts/auth-layout/auth-layout.component';
import {
  canActivate,
  hasCustomClaim,
  redirectLoggedInTo,
  redirectUnauthorizedTo,
} from '@angular/fire/auth-guard';
import { ApprovedUserGuard, AdminOnlyGuard, RoleGuard, RedirectApprovedGuard } from './guards/approved-user.guard';

// Legacy guards (kept for compatibility)
const adminOnly = () => hasCustomClaim('admin');
const redirectUnauthorizedToLogin = () =>
  redirectUnauthorizedTo(['auth/login']);
const redirectLoggedInToDashboard = () => redirectLoggedInTo(['/dashboard']);
const belongsToAccount = (next) => hasCustomClaim(`account-${next.params.id}`);

// New custom guards for pending registration system
const approvedUsersOnly = () => canActivate(() => [ApprovedUserGuard]);
const adminOnlyAccess = () => canActivate(() => [AdminOnlyGuard]);
const redirectApprovedUsers = () => canActivate(() => [RedirectApprovedGuard]);
const requireRoles = (roles: string[]) => canActivate(() => [RoleGuard], { data: { roles } });

export const appRoutes: Route[] = [
  // 1. Landing Page Layout (no header/footer)
  {
    path: '',
    component: LandingLayoutComponent,
    ...redirectApprovedUsers(), // Redirect approved users to dashboard
    children: [
      {
        path: '',
        pathMatch: 'full',
        loadComponent: () =>
          import('./pages/landing/landing').then((m) => m.Landing),
      },
    ],
  },

  // 2. Auth Layout (header/footer, no sidenav)
  {
    path: 'auth',
    component: AuthLayoutComponent,
    children: [
      {
        path: 'login',
        ...redirectApprovedUsers(), // Redirect approved users away from login
        loadComponent: () =>
          import('./pages/auth/login/login').then((m) => m.Login),
      },
      {
        path: 'register',
        ...redirectApprovedUsers(), // Redirect approved users away from register
        loadComponent: () =>
          import('./pages/auth/register/register').then((m) => m.Register),
      },
      {
        path: 'pending-approval',
        // No guard - anyone can access this to check their status
        loadComponent: () =>
          import('./pages/auth/pending-approval/pending-approval').then(
            (m) => m.PendingApproval
          ),
      },
      {
        path: 'forgot-password',
        loadComponent: () =>
          import('./pages/auth/forgot-password/forgot-password').then(
            (m) => m.ForgotPassword
          ),
      },
      {
        path: 'terms-and-conditions',
        loadComponent: () =>
          import('./pages/auth/terms-and-conditions/terms-and-conditions').then(
            (m) => m.TermsAndConditions
          ),
      },
    ],
  },

  // 3. Main App Layout (header/footer/sidenav) - Protected routes
  {
    path: '',
    component: MainLayoutComponent,
    ...approvedUsersOnly(), // Only approved users can access main app
    children: [
      {
        path: 'dashboard',
        loadComponent: () =>
          import('./pages/dashboard/dashboard').then((m) => m.Dashboard),
      },
      {
        path: 'registrations',
        children: [
          {
            path: 'institute',
            loadComponent: () =>
              import(
                './pages/registrations/institute-registration/institute-registration'
              ).then((m) => m.InstituteRegistration),
          },
          {
            path: 'student',
            loadComponent: () =>
              import(
                './pages/registrations/student-registration/student-registration'
              ).then((m) => m.StudentRegistration),
          },
          {
            path: 'trainer',
            loadComponent: () =>
              import(
                './pages/registrations/trainer-registration/trainer-registration'
              ).then((m) => m.TrainerRegistration),
          },
          {
            path: 'parent',
            loadComponent: () =>
              import(
                './pages/registrations/parent-registration/parent-registration'
              ).then((m) => m.ParentRegistration),
          },
          {
            path: 'employee',
            loadComponent: () =>
              import(
                './pages/registrations/employee-registration/employee-registration'
              ).then((m) => m.EmployeeRegistration),
          },
        ],
      },
      {
        path: 'system',
        children: [
          {
            path: 'settings',
            loadComponent: () =>
              import('./pages/settings/settings').then((m) => m.Settings),
          },
        ],
      },
      {
        path: 'home',
        redirectTo: 'dashboard',
        pathMatch: 'full',
      },
    ],
  },
];
