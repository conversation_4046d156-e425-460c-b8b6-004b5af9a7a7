import { Injectable, inject } from '@angular/core';
import { Functions, httpsCallable } from '@angular/fire/functions';
import { Auth } from '@angular/fire/auth';
import { Firestore, collection, query, where, onSnapshot, orderBy } from '@angular/fire/firestore';
import { Observable, BehaviorSubject } from 'rxjs';

/**
 * Interface for pending registration data
 */
export interface PendingRegistration {
  uid: string;
  email: string;
  name: string;
  role: 'student' | 'trainer' | 'parent' | 'college-staff';
  status: 'pending' | 'approved' | 'rejected';
  submittedAt: any; // Firestore Timestamp
  verificationData?: {
    [key: string]: any; // Allow dynamic properties
    studentId?: string;
    institutionEmail?: string;
    credentials?: string[];
    parentStudentLink?: string;
    requiresCredentials?: boolean;
    requiresInstitutionalEmail?: boolean;
    requiresStudentLinkage?: boolean;
    requiresExperienceVerification?: boolean;
    requiresDepartmentVerification?: boolean;
    requiresStudentId?: boolean;
  };
  approvedBy?: string;
  approvedAt?: any; // Firestore Timestamp
  rejectedBy?: string;
  rejectedAt?: any; // Firestore Timestamp
  rejectionReason?: string;
}

/**
 * Interface for registration status response
 */
export interface RegistrationStatus {
  status: 'pending' | 'approved' | 'rejected' | 'not_found';
  message?: string;
  registrationData?: {
    name: string;
    email: string;
    role: string;
    submittedAt: any;
    approvedAt?: any;
    rejectedAt?: any;
    rejectionReason?: string;
  };
  currentClaims?: any;
}

/**
 * Service for managing pending registrations
 * Handles creation, approval, rejection, and status checking of user registrations
 */
@Injectable({
  providedIn: 'root'
})
export class PendingRegistrationService {
  private functions: Functions = inject(Functions);
  private auth: Auth = inject(Auth);
  private firestore: Firestore = inject(Firestore);

  // BehaviorSubject to track pending registrations for admin dashboard
  private pendingRegistrationsSubject = new BehaviorSubject<PendingRegistration[]>([]);
  public pendingRegistrations$ = this.pendingRegistrationsSubject.asObservable();

  // BehaviorSubject to track current user's registration status
  private userRegistrationStatusSubject = new BehaviorSubject<RegistrationStatus | null>(null);
  public userRegistrationStatus$ = this.userRegistrationStatusSubject.asObservable();

  constructor() {
    // Initialize real-time listeners when service is created
    this.initializePendingRegistrationsListener();
  }

  /**
   * Creates a pending registration for a new user
   * This replaces the immediate role assignment in the original registration flow
   *
   * @param userData - User registration data
   * @returns Promise with registration result
   */
  async createPendingRegistration(userData: {
    uid: string;
    email: string;
    name: string;
    role: string;
    verificationData?: any;
  }): Promise<any> {
    try {
      const createPendingRegistration = httpsCallable(this.functions, 'createPendingRegistration');
      const result = await createPendingRegistration(userData);

      console.log('Pending registration created:', result.data);
      return result.data;
    } catch (error) {
      console.error('Error creating pending registration:', error);
      throw error;
    }
  }

  /**
   * Approves a pending registration (Admin only)
   * Grants the user access with their requested role
   *
   * @param registrationId - UID of the user to approve
   * @returns Promise with approval result
   */
  async approvePendingRegistration(registrationId: string): Promise<any> {
    try {
      const approvePendingRegistration = httpsCallable(this.functions, 'approvePendingRegistration');
      const result = await approvePendingRegistration({ registrationId });

      console.log('Registration approved:', result.data);
      return result.data;
    } catch (error) {
      console.error('Error approving registration:', error);
      throw error;
    }
  }

  /**
   * Rejects a pending registration (Admin only)
   * Denies access and provides rejection reason
   *
   * @param registrationId - UID of the user to reject
   * @param reason - Reason for rejection
   * @returns Promise with rejection result
   */
  async rejectPendingRegistration(registrationId: string, reason: string): Promise<any> {
    try {
      const rejectPendingRegistration = httpsCallable(this.functions, 'rejectPendingRegistration');
      const result = await rejectPendingRegistration({ registrationId, reason });

      console.log('Registration rejected:', result.data);
      return result.data;
    } catch (error) {
      console.error('Error rejecting registration:', error);
      throw error;
    }
  }

  /**
   * Gets the current user's registration status
   * Users can only check their own status
   *
   * @returns Promise with registration status
   */
  async getCurrentUserRegistrationStatus(): Promise<RegistrationStatus> {
    try {
      const getRegistrationStatus = httpsCallable(this.functions, 'getRegistrationStatus');
      const result = await getRegistrationStatus({});

      const status = result.data as RegistrationStatus;
      this.userRegistrationStatusSubject.next(status);

      console.log('User registration status:', status);
      return status;
    } catch (error) {
      console.error('Error getting registration status:', error);
      throw error;
    }
  }

  /**
   * Initializes real-time listener for pending registrations (Admin dashboard)
   * Only listens when user is authenticated and is an admin
   */
  private initializePendingRegistrationsListener(): void {
    // Listen to auth state changes
    this.auth.onAuthStateChanged(async (user) => {
      if (user) {
        try {
          // Check if user is admin
          const tokenResult = await user.getIdTokenResult();
          const isAdmin = tokenResult.claims?.['role'] === 'admin';

          if (isAdmin) {
            this.startPendingRegistrationsListener();
          }
        } catch (error) {
          console.error('Error checking admin status:', error);
        }
      } else {
        // User logged out, clear data
        this.pendingRegistrationsSubject.next([]);
        this.userRegistrationStatusSubject.next(null);
      }
    });
  }

  /**
   * Starts real-time listener for pending registrations
   * Used by admin dashboard to show live updates
   */
  private startPendingRegistrationsListener(): void {
    const pendingRegistrationsCollection = collection(this.firestore, 'pendingRegistrations');

    // Query for pending registrations, ordered by submission date
    const q = query(
      pendingRegistrationsCollection,
      where('status', '==', 'pending'),
      orderBy('submittedAt', 'desc')
    );

    // Set up real-time listener
    onSnapshot(q, (snapshot) => {
      const pendingRegistrations: PendingRegistration[] = [];

      snapshot.forEach((doc) => {
        const data = doc.data() as PendingRegistration;
        pendingRegistrations.push({
          ...data,
          uid: doc.id // Document ID is the user UID
        });
      });

      console.log('Pending registrations updated:', pendingRegistrations.length);
      this.pendingRegistrationsSubject.next(pendingRegistrations);
    }, (error) => {
      console.error('Error listening to pending registrations:', error);
    });
  }

  /**
   * Gets all pending registrations (Admin only)
   * Returns current snapshot without setting up listener
   *
   * @returns Observable of pending registrations
   */
  getPendingRegistrations(): Observable<PendingRegistration[]> {
    return this.pendingRegistrations$;
  }

  /**
   * Checks if current user has admin privileges
   * Used to determine if admin features should be shown
   *
   * @returns Promise<boolean> - true if user is admin
   */
  async isCurrentUserAdmin(): Promise<boolean> {
    try {
      const user = this.auth.currentUser;
      if (!user) return false;

      const tokenResult = await user.getIdTokenResult();
      return tokenResult.claims?.['role'] === 'admin';
    } catch (error) {
      console.error('Error checking admin status:', error);
      return false;
    }
  }

  /**
   * Checks if current user's registration is approved
   * Used by route guards to control access
   *
   * @returns Promise<boolean> - true if user is approved
   */
  async isCurrentUserApproved(): Promise<boolean> {
    try {
      const user = this.auth.currentUser;
      if (!user) return false;

      const tokenResult = await user.getIdTokenResult();
      return tokenResult.claims?.['status'] === 'approved';
    } catch (error) {
      console.error('Error checking approval status:', error);
      return false;
    }
  }

  /**
   * Gets current user's role and status from token claims
   * Used throughout the app to determine user permissions
   *
   * @returns Promise with user claims
   */
  async getCurrentUserClaims(): Promise<any> {
    try {
      const user = this.auth.currentUser;
      if (!user) return null;

      const tokenResult = await user.getIdTokenResult();
      return tokenResult.claims;
    } catch (error) {
      console.error('Error getting user claims:', error);
      return null;
    }
  }
}
