import * as functions from 'firebase-functions';
import * as admin from 'firebase-admin';

// Initialize Firebase Admin if not already initialized
if (!admin.apps.length) {
  admin.initializeApp();
}

const db = admin.firestore();

/**
 * Interface for pending registration data
 */
interface PendingRegistrationData {
  uid: string;
  email: string;
  name: string;
  role: 'student' | 'trainer' | 'parent' | 'college-staff';
  status: 'pending' | 'approved' | 'rejected';
  submittedAt: admin.firestore.Timestamp;
  verificationData?: {
    studentId?: string;
    institutionEmail?: string;
    credentials?: string[];
    parentStudentLink?: string;
  };
  approvedBy?: string;
  approvedAt?: admin.firestore.Timestamp;
  rejectedBy?: string;
  rejectedAt?: admin.firestore.Timestamp;
  rejectionReason?: string;
}

/**
 * Creates a pending registration record in Firestore
 * This function is called during the registration process to store user data
 * before admin approval
 */
export const createPendingRegistration = functions.https.onCall(
  async (request: any, context: any) => {
    const { uid, email, name, role, verificationData } = request.data;
    
    // Validate required fields
    if (!uid || !email || !name || !role) {
      throw new functions.https.HttpsError(
        'invalid-argument',
        'Missing required fields: uid, email, name, and role are required.'
      );
    }

    // Validate role is one of the allowed values
    const allowedRoles = ['student', 'trainer', 'parent', 'college-staff'];
    if (!allowedRoles.includes(role)) {
      throw new functions.https.HttpsError(
        'invalid-argument',
        `Invalid role. Must be one of: ${allowedRoles.join(', ')}`
      );
    }

    try {
      // Create pending registration document
      const pendingRegistration: PendingRegistrationData = {
        uid,
        email,
        name,
        role,
        status: 'pending',
        submittedAt: admin.firestore.Timestamp.now(),
        verificationData: verificationData || {}
      };

      // Store in Firestore
      await db.collection('pendingRegistrations').doc(uid).set(pendingRegistration);

      // Set user claims to pending status (no role access yet)
      await admin.auth().setCustomUserClaims(uid, {
        status: 'pending',
        registeredAt: Date.now()
      });

      console.log(`Pending registration created for user ${uid} with role ${role}`);

      return {
        success: true,
        message: 'Registration submitted successfully. Please wait for admin approval.',
        registrationId: uid
      };

    } catch (error) {
      console.error('Error creating pending registration:', error);
      throw new functions.https.HttpsError(
        'internal',
        'Unable to create pending registration.'
      );
    }
  }
);

/**
 * Approves a pending registration and grants user access
 * Only admins can call this function
 */
export const approvePendingRegistration = functions.https.onCall(
  async (request: any, context: any) => {
    // Verify the caller is authenticated
    if (!context.auth) {
      throw new functions.https.HttpsError(
        'unauthenticated',
        'User must be authenticated to approve registrations.'
      );
    }

    // Verify the caller is an admin
    const adminToken = await admin.auth().getUser(context.auth.uid);
    const adminClaims = adminToken.customClaims;
    
    if (!adminClaims?.role || adminClaims.role !== 'admin') {
      throw new functions.https.HttpsError(
        'permission-denied',
        'Only admins can approve registrations.'
      );
    }

    const { registrationId } = request.data;

    if (!registrationId) {
      throw new functions.https.HttpsError(
        'invalid-argument',
        'Registration ID is required.'
      );
    }

    try {
      // Get the pending registration
      const pendingDoc = await db.collection('pendingRegistrations').doc(registrationId).get();
      
      if (!pendingDoc.exists) {
        throw new functions.https.HttpsError(
          'not-found',
          'Pending registration not found.'
        );
      }

      const pendingData = pendingDoc.data() as PendingRegistrationData;

      // Check if already processed
      if (pendingData.status !== 'pending') {
        throw new functions.https.HttpsError(
          'failed-precondition',
          `Registration is already ${pendingData.status}.`
        );
      }

      // Update the pending registration status
      await db.collection('pendingRegistrations').doc(registrationId).update({
        status: 'approved',
        approvedBy: context.auth.uid,
        approvedAt: admin.firestore.Timestamp.now()
      });

      // Grant user access by setting role and approved status in custom claims
      await admin.auth().setCustomUserClaims(pendingData.uid, {
        role: pendingData.role,
        status: 'approved',
        approvedAt: Date.now(),
        approvedBy: context.auth.uid
      });

      console.log(`Registration approved for user ${pendingData.uid} with role ${pendingData.role}`);

      return {
        success: true,
        message: `Registration approved for ${pendingData.name} as ${pendingData.role}.`,
        approvedUser: {
          uid: pendingData.uid,
          name: pendingData.name,
          email: pendingData.email,
          role: pendingData.role
        }
      };

    } catch (error) {
      console.error('Error approving registration:', error);
      
      // Re-throw Firebase errors
      if (error instanceof functions.https.HttpsError) {
        throw error;
      }
      
      throw new functions.https.HttpsError(
        'internal',
        'Unable to approve registration.'
      );
    }
  }
);

/**
 * Rejects a pending registration
 * Only admins can call this function
 */
export const rejectPendingRegistration = functions.https.onCall(
  async (request: any, context: any) => {
    // Verify the caller is authenticated
    if (!context.auth) {
      throw new functions.https.HttpsError(
        'unauthenticated',
        'User must be authenticated to reject registrations.'
      );
    }

    // Verify the caller is an admin
    const adminToken = await admin.auth().getUser(context.auth.uid);
    const adminClaims = adminToken.customClaims;
    
    if (!adminClaims?.role || adminClaims.role !== 'admin') {
      throw new functions.https.HttpsError(
        'permission-denied',
        'Only admins can reject registrations.'
      );
    }

    const { registrationId, reason } = request.data;

    if (!registrationId) {
      throw new functions.https.HttpsError(
        'invalid-argument',
        'Registration ID is required.'
      );
    }

    if (!reason || reason.trim().length === 0) {
      throw new functions.https.HttpsError(
        'invalid-argument',
        'Rejection reason is required.'
      );
    }

    try {
      // Get the pending registration
      const pendingDoc = await db.collection('pendingRegistrations').doc(registrationId).get();
      
      if (!pendingDoc.exists) {
        throw new functions.https.HttpsError(
          'not-found',
          'Pending registration not found.'
        );
      }

      const pendingData = pendingDoc.data() as PendingRegistrationData;

      // Check if already processed
      if (pendingData.status !== 'pending') {
        throw new functions.https.HttpsError(
          'failed-precondition',
          `Registration is already ${pendingData.status}.`
        );
      }

      // Update the pending registration status
      await db.collection('pendingRegistrations').doc(registrationId).update({
        status: 'rejected',
        rejectedBy: context.auth.uid,
        rejectedAt: admin.firestore.Timestamp.now(),
        rejectionReason: reason.trim()
      });

      // Update user claims to rejected status
      await admin.auth().setCustomUserClaims(pendingData.uid, {
        status: 'rejected',
        rejectedAt: Date.now(),
        rejectedBy: context.auth.uid,
        rejectionReason: reason.trim()
      });

      console.log(`Registration rejected for user ${pendingData.uid}. Reason: ${reason}`);

      return {
        success: true,
        message: `Registration rejected for ${pendingData.name}.`,
        rejectedUser: {
          uid: pendingData.uid,
          name: pendingData.name,
          email: pendingData.email,
          role: pendingData.role,
          rejectionReason: reason.trim()
        }
      };

    } catch (error) {
      console.error('Error rejecting registration:', error);
      
      // Re-throw Firebase errors
      if (error instanceof functions.https.HttpsError) {
        throw error;
      }
      
      throw new functions.https.HttpsError(
        'internal',
        'Unable to reject registration.'
      );
    }
  }
);

/**
 * Gets the registration status for the current user
 * Users can only check their own status
 */
export const getRegistrationStatus = functions.https.onCall(
  async (request: any, context: any) => {
    // Verify the caller is authenticated
    if (!context.auth) {
      throw new functions.https.HttpsError(
        'unauthenticated',
        'User must be authenticated to check registration status.'
      );
    }

    try {
      const uid = context.auth.uid;
      
      // Get user's custom claims for current status
      const userRecord = await admin.auth().getUser(uid);
      const claims = userRecord.customClaims || {};

      // Get pending registration details if exists
      const pendingDoc = await db.collection('pendingRegistrations').doc(uid).get();
      
      if (!pendingDoc.exists) {
        return {
          status: 'not_found',
          message: 'No registration found for this user.'
        };
      }

      const pendingData = pendingDoc.data() as PendingRegistrationData;

      return {
        status: pendingData.status,
        registrationData: {
          name: pendingData.name,
          email: pendingData.email,
          role: pendingData.role,
          submittedAt: pendingData.submittedAt,
          approvedAt: pendingData.approvedAt,
          rejectedAt: pendingData.rejectedAt,
          rejectionReason: pendingData.rejectionReason
        },
        currentClaims: claims
      };

    } catch (error) {
      console.error('Error getting registration status:', error);
      throw new functions.https.HttpsError(
        'internal',
        'Unable to get registration status.'
      );
    }
  }
);
